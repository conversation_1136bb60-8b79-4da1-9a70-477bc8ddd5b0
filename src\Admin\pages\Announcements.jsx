import styled from "styled-components";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const AddButton = styled.button`
  background-color: #64748b;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #475569;
  }
`;

const AnnouncementCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-left: 4px solid ${props => props.$priority === 'high' ? '#dc2626' : props.$priority === 'medium' ? '#f59e0b' : '#64748b'};
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  }
`;

const AnnouncementHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
`;

const AnnouncementTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
`;

const AnnouncementMeta = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 12px;
  color: #64748b;
`;

const PriorityBadge = styled.span`
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => 
    props.$priority === 'high' ? '#fee2e2' : 
    props.$priority === 'medium' ? '#fef3c7' : '#f1f5f9'};
  color: ${props => 
    props.$priority === 'high' ? '#dc2626' : 
    props.$priority === 'medium' ? '#92400e' : '#64748b'};
`;

const AnnouncementContent = styled.p`
  margin: 0;
  color: #475569;
  line-height: 1.6;
`;

const AnnouncementsPage = () => {
  const navigate = useNavigate();
  const [announcements] = useState([
    {
      id: 1,
      title: "New COVID-19 Safety Protocols",
      content: "All staff must follow updated safety protocols including mandatory mask wearing in patient areas and regular sanitization procedures.",
      priority: "high",
      date: "2024-01-15",
      author: "Dr. Sarah Johnson"
    },
    {
      id: 2,
      title: "Emergency Preparedness Drill",
      content: "Monthly emergency drill scheduled for January 20th at 2:00 PM. All departments must participate.",
      priority: "medium",
      date: "2024-01-12",
      author: "Admin Team"
    },
    {
      id: 3,
      title: "Staff Meeting - February Schedule",
      content: "Department heads meeting scheduled for February 1st to discuss quarterly objectives and resource allocation.",
      priority: "low",
      date: "2024-01-10",
      author: "HR Department"
    }
  ]);

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem onClick={() => navigate("/")}>
            <span>📊</span> Dashboard
          </NavItem>
          <NavItem onClick={() => navigate("/staff")}>
            <span>👥</span> Staff
          </NavItem>
          <NavItem className="active">
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📈</span> Reports
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Announcements</PageTitle>
          <AddButton>
            <span>+</span> New Announcement
          </AddButton>
        </TopBar>

        {announcements.map((announcement) => (
          <AnnouncementCard key={announcement.id} $priority={announcement.priority}>
            <AnnouncementHeader>
              <AnnouncementTitle>{announcement.title}</AnnouncementTitle>
              <AnnouncementMeta>
                <PriorityBadge $priority={announcement.priority}>
                  {announcement.priority}
                </PriorityBadge>
                <span>{announcement.date}</span>
                <span>by {announcement.author}</span>
              </AnnouncementMeta>
            </AnnouncementHeader>
            <AnnouncementContent>{announcement.content}</AnnouncementContent>
          </AnnouncementCard>
        ))}
      </Main>
    </Layout>
  );
};

export default AnnouncementsPage;
