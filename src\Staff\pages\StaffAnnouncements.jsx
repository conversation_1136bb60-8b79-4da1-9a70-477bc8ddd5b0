import styled from "styled-components";
import { useState } from "react";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #ffffff;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #64748b;
  }
`;

const AnnouncementCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-left: 4px solid ${props => 
    props.$priority === 'high' ? '#dc2626' : 
    props.$priority === 'medium' ? '#f59e0b' : '#64748b'};
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  }
`;

const AnnouncementHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
`;

const AnnouncementTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
`;

const AnnouncementMeta = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
`;

const PriorityBadge = styled.span`
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => 
    props.$priority === 'high' ? '#fee2e2' : 
    props.$priority === 'medium' ? '#fef3c7' : '#f1f5f9'};
  color: ${props => 
    props.$priority === 'high' ? '#dc2626' : 
    props.$priority === 'medium' ? '#92400e' : '#64748b'};
`;

const AnnouncementDate = styled.div`
  font-size: 12px;
  color: #64748b;
`;

const AnnouncementContent = styled.p`
  margin: 0 0 12px 0;
  color: #475569;
  line-height: 1.6;
`;

const AnnouncementFooter = styled.div`
  font-size: 12px;
  color: #64748b;
  border-top: 1px solid #f1f5f9;
  padding-top: 12px;
`;

const ReadStatus = styled.span`
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.$read ? '#dcfce7' : '#fef3c7'};
  margin-right: 8px;
`;

const StaffAnnouncementsPage = () => {
  const [filter, setFilter] = useState('all');
  const [announcements] = useState([
    {
      id: 1,
      title: "New COVID-19 Safety Protocols",
      content: "All staff must follow updated safety protocols including mandatory mask wearing in patient areas and regular sanitization procedures. Please review the attached guidelines and ensure compliance.",
      priority: "high",
      date: "2024-01-15",
      author: "Dr. Sarah Johnson",
      department: "Administration",
      read: false
    },
    {
      id: 2,
      title: "Emergency Preparedness Drill",
      content: "Monthly emergency drill scheduled for January 20th at 2:00 PM. All departments must participate. Please ensure you know your evacuation routes and assembly points.",
      priority: "medium",
      date: "2024-01-12",
      author: "Admin Team",
      department: "Safety",
      read: true
    },
    {
      id: 3,
      title: "Staff Meeting - February Schedule",
      content: "Department heads meeting scheduled for February 1st to discuss quarterly objectives and resource allocation. Individual department meetings will follow.",
      priority: "low",
      date: "2024-01-10",
      author: "HR Department",
      department: "Human Resources",
      read: true
    },
    {
      id: 4,
      title: "New Equipment Training Session",
      content: "Training session for the new MRI equipment will be conducted on January 25th. All radiology staff are required to attend. Additional sessions will be scheduled if needed.",
      priority: "medium",
      date: "2024-01-08",
      author: "Dr. Michael Chen",
      department: "Radiology",
      read: false
    }
  ]);

  const filteredAnnouncements = announcements.filter(announcement => {
    if (filter === 'unread') return !announcement.read;
    if (filter === 'high') return announcement.priority === 'high';
    return true;
  });

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem>
            <span>👤</span> Profile
          </NavItem>
          <NavItem>
            <span>💰</span> Payslip
          </NavItem>
          <NavItem>
            <span>🏛️</span> Pension
          </NavItem>
          <NavItem>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem className="active">
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📄</span> Documents
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Announcements</PageTitle>
          <FilterSelect value={filter} onChange={(e) => setFilter(e.target.value)}>
            <option value="all">All Announcements</option>
            <option value="unread">Unread Only</option>
            <option value="high">High Priority</option>
          </FilterSelect>
        </TopBar>

        {filteredAnnouncements.map((announcement) => (
          <AnnouncementCard key={announcement.id} $priority={announcement.priority}>
            <AnnouncementHeader>
              <AnnouncementTitle>
                <ReadStatus $read={announcement.read} />
                {announcement.title}
              </AnnouncementTitle>
              <AnnouncementMeta>
                <PriorityBadge $priority={announcement.priority}>
                  {announcement.priority}
                </PriorityBadge>
                <AnnouncementDate>{announcement.date}</AnnouncementDate>
              </AnnouncementMeta>
            </AnnouncementHeader>
            <AnnouncementContent>{announcement.content}</AnnouncementContent>
            <AnnouncementFooter>
              Posted by {announcement.author} • {announcement.department}
            </AnnouncementFooter>
          </AnnouncementCard>
        ))}
      </Main>
    </Layout>
  );
};

export default StaffAnnouncementsPage;
