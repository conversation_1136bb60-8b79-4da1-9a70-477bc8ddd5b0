import styled from "styled-components";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
    sans-serif;
  color: #1a1a1a;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
`;

const SidebarLogo = styled.div`
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 40px;
  color: #1e293b;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  span {
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);

    &::before {
      content: "";
      position: absolute;
      left: -24px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background: #3b82f6;
      border-radius: 2px;
    }
  }

  &:hover:not(.active) {
    background-color: rgba(59, 130, 246, 0.08);
    color: #3b82f6;
    transform: translateX(4px);
  }

  span {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.02);
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const WelcomeSection = styled.div`
  h1 {
    font-size: 32px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const NotificationIcon = styled.div`
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 40px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  }
`;

const ProfileAvatar = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  border: 4px solid rgba(255, 255, 255, 0.9);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.h2`
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const ProfileDetails = styled.div`
  display: flex;
  gap: 40px;

  div {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 500;
  }

  .value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
`;

const InfoCard = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
`;

const InfoIcon = styled.div`
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${(props) =>
    props.$bgColor || "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"};
  color: ${(props) => props.$iconColor || "#ffffff"};
  font-size: 28px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1) rotate(5deg);
  }
`;

const InfoContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const InfoLabel = styled.div`
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
`;

const InfoValue = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: #333;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
`;

const DetailCard = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 28px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
`;

const DetailHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;

const DetailIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => props.$bgColor || "#e3f2fd"};
  color: ${(props) => props.$iconColor || "#1976d2"};
  font-size: 16px;
`;

const DetailTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
`;

const DetailValue = styled.div`
  font-size: 20px;
  font-weight: 600;
  color: #333;
`;

const StaffDashboard = () => {
  // Sample staff data - in a real app, this would come from an API
  const staffData = {
    name: "John Smith",
    avatar: "https://i.pravatar.cc/100?img=32",
    oracleNumber: "024356789",
    dateOfEmployment: "March 15, 2018",
    level: "Senior",
    qualifications: "MBBS, MD",
    salary: "$85,000",
    pension: "$20,000",
    yearsInService: 6,
    loans: "$5,000",
  };

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>

        <nav>
          <NavItem className="active">
            <span>👤</span> Profile
          </NavItem>
          <NavItem>
            <span>💰</span> Payslip
          </NavItem>
          <NavItem>
            <span>🏛️</span> Pension
          </NavItem>
          <NavItem>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📄</span> Documents
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <WelcomeSection>
            <h1>Welcome, {staffData.name}</h1>
          </WelcomeSection>
          <UserSection>
            <NotificationIcon>🔔</NotificationIcon>
            <Avatar>
              <img src={staffData.avatar} alt="User" />
            </Avatar>
          </UserSection>
        </TopBar>

        <ProfileHeader>
          <ProfileAvatar>
            <img src={staffData.avatar} alt={staffData.name} />
          </ProfileAvatar>
          <ProfileInfo>
            <ProfileName>{staffData.name}</ProfileName>
            <ProfileDetails>
              <div>
                <span className="label">Oracle Number</span>
                <span className="value">{staffData.oracleNumber}</span>
              </div>
              <div>
                <span className="label">Date of Employment</span>
                <span className="value">{staffData.dateOfEmployment}</span>
              </div>
            </ProfileDetails>
          </ProfileInfo>
        </ProfileHeader>

        <InfoGrid>
          <InfoCard>
            <InfoIcon
              $bgColor="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
              $iconColor="#ffffff"
            >
              🆔
            </InfoIcon>
            <InfoContent>
              <InfoLabel>Oracle Number</InfoLabel>
              <InfoValue>{staffData.oracleNumber}</InfoValue>
            </InfoContent>
          </InfoCard>

          <InfoCard>
            <InfoIcon
              $bgColor="linear-gradient(135deg, #10b981 0%, #059669 100%)"
              $iconColor="#ffffff"
            >
              📊
            </InfoIcon>
            <InfoContent>
              <InfoLabel>Level</InfoLabel>
              <InfoValue>{staffData.level}</InfoValue>
            </InfoContent>
          </InfoCard>

          <InfoCard>
            <InfoIcon
              $bgColor="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
              $iconColor="#ffffff"
            >
              🎓
            </InfoIcon>
            <InfoContent>
              <InfoLabel>Qualifications</InfoLabel>
              <InfoValue>{staffData.qualifications}</InfoValue>
            </InfoContent>
          </InfoCard>
        </InfoGrid>

        <DetailsGrid>
          <DetailCard>
            <DetailHeader>
              <DetailIcon
                $bgColor="linear-gradient(135deg, #10b981 0%, #059669 100%)"
                $iconColor="#ffffff"
              >
                💰
              </DetailIcon>
              <DetailTitle>Salary</DetailTitle>
            </DetailHeader>
            <DetailValue>{staffData.salary}</DetailValue>
          </DetailCard>

          <DetailCard>
            <DetailHeader>
              <DetailIcon
                $bgColor="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
                $iconColor="#ffffff"
              >
                🏛️
              </DetailIcon>
              <DetailTitle>Pension</DetailTitle>
            </DetailHeader>
            <DetailValue>{staffData.pension}</DetailValue>
          </DetailCard>

          <DetailCard>
            <DetailHeader>
              <DetailIcon
                $bgColor="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
                $iconColor="#ffffff"
              >
                ⏱️
              </DetailIcon>
              <DetailTitle>Years in Service</DetailTitle>
            </DetailHeader>
            <DetailValue>{staffData.yearsInService}</DetailValue>
          </DetailCard>

          <DetailCard>
            <DetailHeader>
              <DetailIcon
                $bgColor="linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"
                $iconColor="#ffffff"
              >
                💳
              </DetailIcon>
              <DetailTitle>Loans</DetailTitle>
            </DetailHeader>
            <DetailValue>{staffData.loans}</DetailValue>
          </DetailCard>
        </DetailsGrid>
      </Main>
    </Layout>
  );
};

export default StaffDashboard;
