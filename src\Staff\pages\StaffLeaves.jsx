import styled from "styled-components";
import { useState } from "react";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const NewLeaveButton = styled.button`
  background: #64748b;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #475569;
  }
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
`;

const LeaveHistory = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
`;

const SectionHeader = styled.div`
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  
  h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
  }
`;

const LeaveItem = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  
  &:last-child {
    border-bottom: none;
  }
`;

const LeaveHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const LeaveType = styled.h3`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  background: ${props => 
    props.$status === 'approved' ? '#dcfce7' : 
    props.$status === 'pending' ? '#fef3c7' : '#fee2e2'};
  color: ${props => 
    props.$status === 'approved' ? '#166534' : 
    props.$status === 'pending' ? '#92400e' : '#dc2626'};
`;

const LeaveDetails = styled.div`
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
`;

const LeaveBalance = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  padding: 24px;
`;

const BalanceItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
  
  &:last-child {
    border-bottom: none;
  }
`;

const BalanceLabel = styled.span`
  font-size: 14px;
  color: #64748b;
`;

const BalanceValue = styled.span`
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
`;

const StaffLeavesPage = () => {
  const [leaves] = useState([
    {
      id: 1,
      type: "Annual Leave",
      startDate: "2024-02-01",
      endDate: "2024-02-05",
      days: 5,
      reason: "Family vacation",
      status: "approved",
      appliedDate: "2024-01-15"
    },
    {
      id: 2,
      type: "Sick Leave",
      startDate: "2024-01-20",
      endDate: "2024-01-22",
      days: 3,
      reason: "Medical appointment",
      status: "approved",
      appliedDate: "2024-01-18"
    },
    {
      id: 3,
      type: "Emergency Leave",
      startDate: "2024-02-10",
      endDate: "2024-02-12",
      days: 3,
      reason: "Family emergency",
      status: "pending",
      appliedDate: "2024-02-08"
    }
  ]);

  const leaveBalance = {
    annual: { total: 25, used: 8, remaining: 17 },
    sick: { total: 12, used: 3, remaining: 9 },
    emergency: { total: 5, used: 0, remaining: 5 }
  };

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem>
            <span>👤</span> Profile
          </NavItem>
          <NavItem>
            <span>💰</span> Payslip
          </NavItem>
          <NavItem>
            <span>🏛️</span> Pension
          </NavItem>
          <NavItem className="active">
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📄</span> Documents
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>My Leaves</PageTitle>
          <NewLeaveButton>Request New Leave</NewLeaveButton>
        </TopBar>

        <ContentGrid>
          <LeaveHistory>
            <SectionHeader>
              <h2>Leave History</h2>
            </SectionHeader>
            {leaves.map((leave) => (
              <LeaveItem key={leave.id}>
                <LeaveHeader>
                  <LeaveType>{leave.type}</LeaveType>
                  <StatusBadge $status={leave.status}>
                    {leave.status}
                  </StatusBadge>
                </LeaveHeader>
                <LeaveDetails>
                  <div><strong>Duration:</strong> {leave.startDate} to {leave.endDate} ({leave.days} days)</div>
                  <div><strong>Reason:</strong> {leave.reason}</div>
                  <div><strong>Applied:</strong> {leave.appliedDate}</div>
                </LeaveDetails>
              </LeaveItem>
            ))}
          </LeaveHistory>

          <div>
            <LeaveBalance>
              <SectionHeader>
                <h2>Leave Balance</h2>
              </SectionHeader>
              <div style={{ padding: '16px 0' }}>
                <BalanceItem>
                  <BalanceLabel>Annual Leave</BalanceLabel>
                  <BalanceValue>{leaveBalance.annual.remaining}/{leaveBalance.annual.total}</BalanceValue>
                </BalanceItem>
                <BalanceItem>
                  <BalanceLabel>Sick Leave</BalanceLabel>
                  <BalanceValue>{leaveBalance.sick.remaining}/{leaveBalance.sick.total}</BalanceValue>
                </BalanceItem>
                <BalanceItem>
                  <BalanceLabel>Emergency Leave</BalanceLabel>
                  <BalanceValue>{leaveBalance.emergency.remaining}/{leaveBalance.emergency.total}</BalanceValue>
                </BalanceItem>
              </div>
            </LeaveBalance>
          </div>
        </ContentGrid>
      </Main>
    </Layout>
  );
};

export default StaffLeavesPage;
