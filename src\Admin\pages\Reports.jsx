import styled from "styled-components";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const DateFilter = styled.select`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #ffffff;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #64748b;
  }
`;

const ReportsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
`;

const ReportCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  }
`;

const ReportHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;

const ReportIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: ${props => props.$bgColor || '#f1f5f9'};
  color: ${props => props.$iconColor || '#64748b'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
`;

const ReportTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
`;

const ReportDescription = styled.p`
  margin: 0 0 16px 0;
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
`;

const GenerateButton = styled.button`
  width: 100%;
  padding: 10px 16px;
  background: #64748b;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #475569;
  }
`;

const QuickStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
`;

const StatCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  border: 1px solid #e2e8f0;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: #64748b;
`;

const ReportsPage = () => {
  const navigate = useNavigate();
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  const reports = [
    {
      id: 1,
      title: "Staff Attendance Report",
      description: "Detailed attendance records for all staff members including overtime and absences.",
      icon: "👥",
      bgColor: "#dbeafe",
      iconColor: "#1e40af"
    },
    {
      id: 2,
      title: "Leave Summary Report",
      description: "Comprehensive leave analysis including approved, pending, and rejected requests.",
      icon: "📅",
      bgColor: "#dcfce7",
      iconColor: "#166534"
    },
    {
      id: 3,
      title: "Department Performance",
      description: "Performance metrics and KPIs for each department and team.",
      icon: "📊",
      bgColor: "#fef3c7",
      iconColor: "#92400e"
    },
    {
      id: 4,
      title: "Payroll Summary",
      description: "Monthly payroll breakdown including salaries, bonuses, and deductions.",
      icon: "💰",
      bgColor: "#f3e8ff",
      iconColor: "#7c3aed"
    },
    {
      id: 5,
      title: "Training Records",
      description: "Staff training completion status and certification tracking.",
      icon: "🎓",
      bgColor: "#fce7f3",
      iconColor: "#be185d"
    },
    {
      id: 6,
      title: "Compliance Report",
      description: "Regulatory compliance status and audit trail documentation.",
      icon: "📋",
      bgColor: "#ecfdf5",
      iconColor: "#059669"
    }
  ];

  const quickStats = [
    { label: "Total Reports Generated", value: "156" },
    { label: "This Month", value: "23" },
    { label: "Pending Reviews", value: "7" },
    { label: "Automated Reports", value: "12" }
  ];

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem onClick={() => navigate("/")}>
            <span>📊</span> Dashboard
          </NavItem>
          <NavItem onClick={() => navigate("/staff")}>
            <span>👥</span> Staff
          </NavItem>
          <NavItem onClick={() => navigate("/announcements")}>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem onClick={() => navigate("/leaves")}>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem className="active">
            <span>📈</span> Reports
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Reports & Analytics</PageTitle>
          <DateFilter 
            value={selectedPeriod} 
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </DateFilter>
        </TopBar>

        <QuickStats>
          {quickStats.map((stat, index) => (
            <StatCard key={index}>
              <StatValue>{stat.value}</StatValue>
              <StatLabel>{stat.label}</StatLabel>
            </StatCard>
          ))}
        </QuickStats>

        <ReportsGrid>
          {reports.map((report) => (
            <ReportCard key={report.id}>
              <ReportHeader>
                <ReportIcon $bgColor={report.bgColor} $iconColor={report.iconColor}>
                  {report.icon}
                </ReportIcon>
                <ReportTitle>{report.title}</ReportTitle>
              </ReportHeader>
              <ReportDescription>{report.description}</ReportDescription>
              <GenerateButton>Generate Report</GenerateButton>
            </ReportCard>
          ))}
        </ReportsGrid>
      </Main>
    </Layout>
  );
};

export default ReportsPage;
