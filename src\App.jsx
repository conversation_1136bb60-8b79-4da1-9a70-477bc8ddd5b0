import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import AdminDashboard from "./Admin/Dashboard";
import StaffPage from "./Admin/StaffPage";
import GlobalStyle from "./shared/GlobalStyles";
import StaffDashboard from "./Staff/pages/StaffDashboard";

const App = () => {
  return (
    <>
      <GlobalStyle />
      <Router>
        <Routes>
          <Route path="/" element={<AdminDashboard />} />
          <Route path="/staff" element={<StaffPage />} />
          <Route path="/staff-dashboard" element={<StaffDashboard />} />
          {/* Add more routes as needed */}
          {/* Example: <Route path="/another-page" element={<AnotherPage />} /> */}
          <Route path="*" element={<div>404 Not Found</div>} />
        </Routes>
      </Router>
    </>
  );
};

export default App;
