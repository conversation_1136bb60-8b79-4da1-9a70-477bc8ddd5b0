import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import AdminDashboard from "./Admin/Dashboard";
import StaffPage from "./Admin/StaffPage";
import AnnouncementsPage from "./Admin/pages/Announcements";
import LeavesPage from "./Admin/pages/Leaves";
import ReportsPage from "./Admin/pages/Reports";
import GlobalStyle from "./shared/GlobalStyles";
import StaffDashboard from "./Staff/pages/StaffDashboard";
import PayslipPage from "./Staff/pages/Payslip";
import StaffLeavesPage from "./Staff/pages/StaffLeaves";
import StaffAnnouncementsPage from "./Staff/pages/StaffAnnouncements";
import NotFound from "./shared/NotFound";

const App = () => {
  return (
    <>
      <GlobalStyle />
      <Router>
        <Routes>
          {/* Admin Routes */}
          <Route path="/" element={<AdminDashboard />} />
          <Route path="/staff" element={<StaffPage />} />
          <Route path="/announcements" element={<AnnouncementsPage />} />
          <Route path="/leaves" element={<LeavesPage />} />
          <Route path="/reports" element={<ReportsPage />} />

          {/* Staff Portal Routes */}
          <Route path="/staff-dashboard" element={<StaffDashboard />} />
          <Route path="/staff-payslip" element={<PayslipPage />} />
          <Route path="/staff-leaves" element={<StaffLeavesPage />} />
          <Route
            path="/staff-announcements"
            element={<StaffAnnouncementsPage />}
          />

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </>
  );
};

export default App;
