import styled from "styled-components";
import { Link, useNavigate } from "react-router-dom";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
    sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
  min-height: 100vh;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;

  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

const NotificationBell = styled.div`
  font-size: 18px;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  color: #64748b;

  &:hover {
    background-color: #f1f5f9;
    color: #475569;
  }

  &::after {
    content: "";
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    background: #dc2626;
    border-radius: 50%;
    border: 2px solid white;
  }
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid #e2e8f0;

  &:hover {
    border-color: #cbd5e1;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
`;

const StatCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #64748b;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  }
`;

const StatIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${(props) => props.$bgColor || "#f1f5f9"};
  color: ${(props) => props.$iconColor || "#64748b"};
  font-size: 20px;
  border: 1px solid #e2e8f0;
`;

const StatContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  letter-spacing: -0.01em;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
`;

const Card = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  }
`;

const CardHeader = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
`;

const CardTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
`;

const CardContent = styled.div`
  padding: 20px 24px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid #eee;

  th {
    text-align: left;
    padding: 10px 5px;
    font-weight: 500;
    color: #666;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    td {
      padding: 12px 5px;
    }
  }
`;

const AnnouncementList = styled.ul`
  padding: 0;
  margin: 0;
  list-style: none;
`;

const AnnouncementItem = styled.li`
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
`;

const AnnouncementIcon = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: ${(props) => props.$bgColor || "#e0f0ff"};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: ${(props) => props.$iconColor || "#007bff"};
`;

const AnnouncementContent = styled.div`
  flex: 1;
`;

const AnnouncementTitle = styled.div`
  font-weight: 500;
`;

const AnnouncementTime = styled.div`
  font-size: 12px;
  color: #888;
`;

export default function AdminDashboard() {
  const navigate = useNavigate();
  // Sample data
  const staffData = [
    {
      name: "Dr. John Smith",
      oracleNumber: "024356789",
      department: "Cardiology",
      role: "Attendant",
    },
    {
      name: "Dr. Sarah Johnson",
      oracleNumber: "024356789",
      department: "Neurology",
      role: "Resident",
    },
    {
      name: "Michael Brown",
      oracleNumber: "024356789",
      department: "Radiology",
      role: "Nurse",
    },
    {
      name: "Jennifer Wilson",
      oracleNumber: "024356789",
      department: "Pediatrics",
      role: "Nurse",
    },
    {
      name: "Emily Davis",
      oracleNumber: "024356789",
      department: "Oncology",
      role: "Attendant",
    },
  ];

  const announcements = [
    {
      title: "New COVID-19 Protocols",
      time: "1 hour ago",
      icon: "📋",
      color: "#e0f0ff",
    },
    {
      title: "Emergency Preparedness Drill",
      time: "3 hours ago",
      icon: "🚨",
      color: "#ffe0e0",
    },
  ];

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span role="img" aria-label="hospital">
            🏥
          </span>{" "}
          Hospital
        </SidebarLogo>

        <nav>
          <Link to="/" style={{ textDecoration: "none", color: "black" }}>
            <NavItem className="active">
              <span>📊</span> Dashboard
            </NavItem>
          </Link>

          <Link to="/staff" style={{ textDecoration: "none", color: "black" }}>
            <NavItem>
              <span>👥</span> Staff
            </NavItem>
          </Link>
          <NavItem onClick={() => navigate("/announcements")}>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem onClick={() => navigate("/leaves")}>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem onClick={() => navigate("/reports")}>
            <span>📈</span> Reports
          </NavItem>
          <NavItem>
            <span>🔢</span> Oracle Number
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Admin Dashboard</PageTitle>
          <UserSection>
            <NotificationBell role="img" aria-label="notifications">
              🔔
            </NotificationBell>
            <Avatar>
              <img src="https://i.pravatar.cc/100?img=32" alt="User" />
            </Avatar>
          </UserSection>
        </TopBar>

        <StatsGrid>
          <StatCard>
            <StatIcon $bgColor="#dbeafe" $iconColor="#1e40af">
              👥
            </StatIcon>
            <StatContent>
              <StatValue>275</StatValue>
              <StatLabel>Total Staff</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon $bgColor="#cffafe" $iconColor="#0e7490">
              ⏱️
            </StatIcon>
            <StatContent>
              <StatValue>12</StatValue>
              <StatLabel>Pending Retirements</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon $bgColor="#dcfce7" $iconColor="#166534">
              📅
            </StatIcon>
            <StatContent>
              <StatValue>9</StatValue>
              <StatLabel>Leave Requests</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon $bgColor="#fef3c7" $iconColor="#92400e">
              📢
            </StatIcon>
            <StatContent>
              <StatValue>4</StatValue>
              <StatLabel>Recent Announcements</StatLabel>
            </StatContent>
          </StatCard>
        </StatsGrid>

        <ContentGrid>
          <Card>
            <CardHeader>
              <CardTitle
                onClick={() => {
                  navigate("/staff-dashboard", { replace: true });
                }}
              >
                Staff Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHead>
                  <tr>
                    <th>Name</th>
                    <th>Oracle Number</th>
                    <th>Department</th>
                    <th>Role</th>
                  </tr>
                </TableHead>
                <TableBody>
                  {staffData.map((staff, idx) => (
                    <tr key={idx}>
                      <td>{staff.name}</td>
                      <td>{staff.oracleNumber}</td>
                      <td>{staff.department}</td>
                      <td>{staff.role}</td>
                    </tr>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Latest Announcements</CardTitle>
            </CardHeader>
            <CardContent>
              <AnnouncementList>
                {announcements.map((item, index) => (
                  <AnnouncementItem key={index}>
                    <AnnouncementIcon $bgColor={item.color}>
                      {item.icon}
                    </AnnouncementIcon>
                    <AnnouncementContent>
                      <AnnouncementTitle>{item.title}</AnnouncementTitle>
                      <AnnouncementTime>{item.time}</AnnouncementTime>
                    </AnnouncementContent>
                  </AnnouncementItem>
                ))}
              </AnnouncementList>
            </CardContent>
          </Card>
        </ContentGrid>
      </Main>
    </Layout>
  );
}
