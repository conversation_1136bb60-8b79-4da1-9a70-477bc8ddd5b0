import styled from "styled-components";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
    sans-serif;
  color: #1a1a1a;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
`;

const SidebarLogo = styled.div`
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 40px;
  color: #1e293b;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  span {
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);

    &::before {
      content: "";
      position: absolute;
      left: -24px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background: #3b82f6;
      border-radius: 2px;
    }
  }

  &:hover:not(.active) {
    background-color: rgba(59, 130, 246, 0.08);
    color: #3b82f6;
    transform: translateX(4px);
  }

  span {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.02);
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const PageTitle = styled.h1`
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

const NotificationBell = styled.div`
  font-size: 20px;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.1);
  }

  &::after {
    content: "";
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    border: 2px solid white;
  }
`;

const Avatar = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
`;

const StatIcon = styled.div`
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${(props) =>
    props.$bgColor || "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"};
  color: ${(props) => props.$iconColor || "#ffffff"};
  font-size: 28px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1) rotate(5deg);
  }
`;

const StatContent = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
  letter-spacing: -0.02em;
`;

const StatLabel = styled.div`
  font-size: 15px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
`;

const Card = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
`;

const CardHeader = styled.div`
  padding: 24px 28px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(29, 78, 216, 0.05) 100%
  );
`;

const CardTitle = styled.h2`
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const CardContent = styled.div`
  padding: 28px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid #eee;

  th {
    text-align: left;
    padding: 10px 5px;
    font-weight: 500;
    color: #666;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    td {
      padding: 12px 5px;
    }
  }
`;

const AnnouncementList = styled.ul`
  padding: 0;
  margin: 0;
  list-style: none;
`;

const AnnouncementItem = styled.li`
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
`;

const AnnouncementIcon = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: ${(props) => props.$bgColor || "#e0f0ff"};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: ${(props) => props.$iconColor || "#007bff"};
`;

const AnnouncementContent = styled.div`
  flex: 1;
`;

const AnnouncementTitle = styled.div`
  font-weight: 500;
`;

const AnnouncementTime = styled.div`
  font-size: 12px;
  color: #888;
`;

export default function AdminDashboard() {
  const navigate = useNavigate();
  // Sample data
  const staffData = [
    {
      name: "Dr. John Smith",
      oracleNumber: "024356789",
      department: "Cardiology",
      role: "Attendant",
    },
    {
      name: "Dr. Sarah Johnson",
      oracleNumber: "024356789",
      department: "Neurology",
      role: "Resident",
    },
    {
      name: "Michael Brown",
      oracleNumber: "024356789",
      department: "Radiology",
      role: "Nurse",
    },
    {
      name: "Jennifer Wilson",
      oracleNumber: "024356789",
      department: "Pediatrics",
      role: "Nurse",
    },
    {
      name: "Emily Davis",
      oracleNumber: "024356789",
      department: "Oncology",
      role: "Attendant",
    },
  ];

  const announcements = [
    {
      title: "New COVID-19 Protocols",
      time: "1 hour ago",
      icon: "📋",
      color: "#e0f0ff",
    },
    {
      title: "Emergency Preparedness Drill",
      time: "3 hours ago",
      icon: "🚨",
      color: "#ffe0e0",
    },
  ];

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span role="img" aria-label="hospital">
            🏥
          </span>{" "}
          Hospital
        </SidebarLogo>

        <nav>
          <Link to="/" style={{ textDecoration: "none", color: "black" }}>
            <NavItem className="active">
              <span>📊</span> Dashboard
            </NavItem>
          </Link>

          <Link to="/staff" style={{ textDecoration: "none", color: "black" }}>
            <NavItem>
              <span>👥</span> Staff
            </NavItem>
          </Link>
          <NavItem>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📈</span> Reports
          </NavItem>
          <NavItem>
            <span>🔢</span> Oracle Number
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Admin Dashboard</PageTitle>
          <UserSection>
            <NotificationBell role="img" aria-label="notifications">
              🔔
            </NotificationBell>
            <Avatar>
              <img src="https://i.pravatar.cc/100?img=32" alt="User" />
            </Avatar>
          </UserSection>
        </TopBar>

        <StatsGrid>
          <StatCard>
            <StatIcon
              $bgColor="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
              $iconColor="#ffffff"
            >
              👥
            </StatIcon>
            <StatContent>
              <StatValue>275</StatValue>
              <StatLabel>Total Staff</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon
              $bgColor="linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)"
              $iconColor="#ffffff"
            >
              ⏱️
            </StatIcon>
            <StatContent>
              <StatValue>12</StatValue>
              <StatLabel>Pending Retirements</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon
              $bgColor="linear-gradient(135deg, #10b981 0%, #059669 100%)"
              $iconColor="#ffffff"
            >
              📅
            </StatIcon>
            <StatContent>
              <StatValue>9</StatValue>
              <StatLabel>Leave Requests</StatLabel>
            </StatContent>
          </StatCard>

          <StatCard>
            <StatIcon
              $bgColor="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
              $iconColor="#ffffff"
            >
              📢
            </StatIcon>
            <StatContent>
              <StatValue>4</StatValue>
              <StatLabel>Recent Announcements</StatLabel>
            </StatContent>
          </StatCard>
        </StatsGrid>

        <ContentGrid>
          <Card>
            <CardHeader>
              <CardTitle
                onClick={() => {
                  navigate("/staff-dashboard", { replace: true });
                }}
              >
                Staff Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHead>
                  <tr>
                    <th>Name</th>
                    <th>Oracle Number</th>
                    <th>Department</th>
                    <th>Role</th>
                  </tr>
                </TableHead>
                <TableBody>
                  {staffData.map((staff, idx) => (
                    <tr key={idx}>
                      <td>{staff.name}</td>
                      <td>{staff.oracleNumber}</td>
                      <td>{staff.department}</td>
                      <td>{staff.role}</td>
                    </tr>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Latest Announcements</CardTitle>
            </CardHeader>
            <CardContent>
              <AnnouncementList>
                {announcements.map((item, index) => (
                  <AnnouncementItem key={index}>
                    <AnnouncementIcon $bgColor={item.color}>
                      {item.icon}
                    </AnnouncementIcon>
                    <AnnouncementContent>
                      <AnnouncementTitle>{item.title}</AnnouncementTitle>
                      <AnnouncementTime>{item.time}</AnnouncementTime>
                    </AnnouncementContent>
                  </AnnouncementItem>
                ))}
              </AnnouncementList>
            </CardContent>
          </Card>
        </ContentGrid>
      </Main>
    </Layout>
  );
}
