import styled from "styled-components";
import { useNavigate } from "react-router-dom";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f8fafc;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
`;

const ErrorCode = styled.h1`
  font-size: 120px;
  font-weight: 700;
  color: #64748b;
  margin: 0;
  line-height: 1;
`;

const ErrorMessage = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 16px 0 8px 0;
`;

const ErrorDescription = styled.p`
  font-size: 16px;
  color: #64748b;
  margin: 0 0 32px 0;
  text-align: center;
  max-width: 400px;
`;

const BackButton = styled.button`
  background: #64748b;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #475569;
  }
`;

const NotFound = () => {
  const navigate = useNavigate();

  return (
    <Container>
      <ErrorCode>404</ErrorCode>
      <ErrorMessage>Page Not Found</ErrorMessage>
      <ErrorDescription>
        The page you are looking for doesn't exist or has been moved.
      </ErrorDescription>
      <BackButton onClick={() => navigate("/")}>
        Go Back Home
      </BackButton>
    </Container>
  );
};

export default NotFound;
