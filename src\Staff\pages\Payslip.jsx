import styled from "styled-components";
import { useState } from "react";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const MonthSelector = styled.select`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #ffffff;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #64748b;
  }
`;

const PayslipCard = styled.div`
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
`;

const PayslipHeader = styled.div`
  padding: 24px 32px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
`;

const PayslipTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1e293b;
`;

const PayslipMeta = styled.div`
  display: flex;
  gap: 32px;
  font-size: 14px;
  color: #64748b;
`;

const PayslipContent = styled.div`
  padding: 32px;
`;

const Section = styled.div`
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #1e293b;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
`;

const PayslipGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
`;

const PayslipItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
  
  &:last-child {
    border-bottom: none;
  }
`;

const ItemLabel = styled.span`
  font-size: 14px;
  color: #64748b;
`;

const ItemValue = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
`;

const TotalSection = styled.div`
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  margin-top: 24px;
`;

const TotalItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  
  &.final {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    border-top: 2px solid #e2e8f0;
    padding-top: 16px;
    margin-top: 16px;
  }
`;

const DownloadButton = styled.button`
  background: #64748b;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #475569;
  }
`;

const PayslipPage = () => {
  const [selectedMonth, setSelectedMonth] = useState('2024-01');

  const payslipData = {
    employeeName: "John Smith",
    employeeId: "EMP001",
    department: "Cardiology",
    position: "Senior Doctor",
    payPeriod: "January 2024",
    payDate: "January 31, 2024",
    earnings: {
      basicSalary: 75000,
      allowances: 8000,
      overtime: 2000,
      bonus: 0
    },
    deductions: {
      tax: 15000,
      pension: 3750,
      insurance: 1200,
      loan: 5000
    }
  };

  const totalEarnings = Object.values(payslipData.earnings).reduce((sum, value) => sum + value, 0);
  const totalDeductions = Object.values(payslipData.deductions).reduce((sum, value) => sum + value, 0);
  const netPay = totalEarnings - totalDeductions;

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem>
            <span>👤</span> Profile
          </NavItem>
          <NavItem className="active">
            <span>💰</span> Payslip
          </NavItem>
          <NavItem>
            <span>🏛️</span> Pension
          </NavItem>
          <NavItem>
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem>
            <span>📄</span> Documents
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Payslip</PageTitle>
          <MonthSelector 
            value={selectedMonth} 
            onChange={(e) => setSelectedMonth(e.target.value)}
          >
            <option value="2024-01">January 2024</option>
            <option value="2023-12">December 2023</option>
            <option value="2023-11">November 2023</option>
            <option value="2023-10">October 2023</option>
          </MonthSelector>
        </TopBar>

        <PayslipCard>
          <PayslipHeader>
            <PayslipTitle>Payslip for {payslipData.payPeriod}</PayslipTitle>
            <PayslipMeta>
              <span>Employee: {payslipData.employeeName}</span>
              <span>ID: {payslipData.employeeId}</span>
              <span>Department: {payslipData.department}</span>
              <span>Pay Date: {payslipData.payDate}</span>
            </PayslipMeta>
          </PayslipHeader>

          <PayslipContent>
            <PayslipGrid>
              <Section>
                <SectionTitle>Earnings</SectionTitle>
                <PayslipItem>
                  <ItemLabel>Basic Salary</ItemLabel>
                  <ItemValue>${payslipData.earnings.basicSalary.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Allowances</ItemLabel>
                  <ItemValue>${payslipData.earnings.allowances.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Overtime</ItemLabel>
                  <ItemValue>${payslipData.earnings.overtime.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Bonus</ItemLabel>
                  <ItemValue>${payslipData.earnings.bonus.toLocaleString()}</ItemValue>
                </PayslipItem>
              </Section>

              <Section>
                <SectionTitle>Deductions</SectionTitle>
                <PayslipItem>
                  <ItemLabel>Income Tax</ItemLabel>
                  <ItemValue>-${payslipData.deductions.tax.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Pension Contribution</ItemLabel>
                  <ItemValue>-${payslipData.deductions.pension.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Health Insurance</ItemLabel>
                  <ItemValue>-${payslipData.deductions.insurance.toLocaleString()}</ItemValue>
                </PayslipItem>
                <PayslipItem>
                  <ItemLabel>Loan Repayment</ItemLabel>
                  <ItemValue>-${payslipData.deductions.loan.toLocaleString()}</ItemValue>
                </PayslipItem>
              </Section>
            </PayslipGrid>

            <TotalSection>
              <TotalItem>
                <span>Total Earnings:</span>
                <span>${totalEarnings.toLocaleString()}</span>
              </TotalItem>
              <TotalItem>
                <span>Total Deductions:</span>
                <span>-${totalDeductions.toLocaleString()}</span>
              </TotalItem>
              <TotalItem className="final">
                <span>Net Pay:</span>
                <span>${netPay.toLocaleString()}</span>
              </TotalItem>
            </TotalSection>

            <div style={{ marginTop: '24px', textAlign: 'right' }}>
              <DownloadButton>Download PDF</DownloadButton>
            </div>
          </PayslipContent>
        </PayslipCard>
      </Main>
    </Layout>
  );
};

export default PayslipPage;
