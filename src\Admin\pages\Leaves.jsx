import styled from "styled-components";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Layout = styled.div`
  display: flex;
  height: 100vh;
  width: 100vw;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #1a1a1a;
  background: #f8fafc;
`;

const Sidebar = styled.aside`
  width: 280px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  border-right: 1px solid #e2e8f0;
`;

const SidebarLogo = styled.div`
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  color: #334155;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  
  span {
    font-size: 24px;
  }
`;

const NavItem = styled.div`
  padding: 14px 16px;
  border-radius: 12px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    background: #f1f5f9;
    color: #475569;
    border-left: 3px solid #64748b;
    margin-left: -24px;
    padding-left: 21px;
  }

  &:hover:not(.active) {
    background-color: #f8fafc;
    color: #475569;
  }

  span {
    font-size: 18px;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: #f8fafc;
`;

const TopBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.01em;
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 8px;
`;

const FilterTab = styled.button`
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background: ${props => props.$active ? '#64748b' : '#ffffff'};
  color: ${props => props.$active ? '#ffffff' : '#64748b'};
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? '#475569' : '#f8fafc'};
  }
`;

const LeaveTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
`;

const TableHead = styled.thead`
  background-color: #f8fafc;

  th {
    text-align: left;
    padding: 16px;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #f1f5f9;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8fafc;
    }

    td {
      padding: 16px;
      font-size: 14px;
    }
  }
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  background: ${props => 
    props.$status === 'approved' ? '#dcfce7' : 
    props.$status === 'pending' ? '#fef3c7' : '#fee2e2'};
  color: ${props => 
    props.$status === 'approved' ? '#166534' : 
    props.$status === 'pending' ? '#92400e' : '#dc2626'};
`;

const ActionButton = styled.button`
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  color: #64748b;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  margin-right: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
  }

  &.approve {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
    
    &:hover {
      background: #bbf7d0;
    }
  }

  &.reject {
    background: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
    
    &:hover {
      background: #fecaca;
    }
  }
`;

const LeavesPage = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState('all');
  const [leaves] = useState([
    {
      id: 1,
      employeeName: "Dr. John Smith",
      leaveType: "Annual Leave",
      startDate: "2024-02-01",
      endDate: "2024-02-05",
      days: 5,
      reason: "Family vacation",
      status: "pending"
    },
    {
      id: 2,
      employeeName: "Sarah Johnson",
      leaveType: "Sick Leave",
      startDate: "2024-01-20",
      endDate: "2024-01-22",
      days: 3,
      reason: "Medical appointment",
      status: "approved"
    },
    {
      id: 3,
      employeeName: "Michael Brown",
      leaveType: "Emergency Leave",
      startDate: "2024-01-15",
      endDate: "2024-01-16",
      days: 2,
      reason: "Family emergency",
      status: "approved"
    },
    {
      id: 4,
      employeeName: "Jennifer Wilson",
      leaveType: "Annual Leave",
      startDate: "2024-02-10",
      endDate: "2024-02-14",
      days: 5,
      reason: "Personal time off",
      status: "rejected"
    }
  ]);

  const filteredLeaves = leaves.filter(leave => 
    activeFilter === 'all' || leave.status === activeFilter
  );

  return (
    <Layout>
      <Sidebar>
        <SidebarLogo>
          <span>🏥</span> Hospital
        </SidebarLogo>
        
        <nav>
          <NavItem onClick={() => navigate("/")}>
            <span>📊</span> Dashboard
          </NavItem>
          <NavItem onClick={() => navigate("/staff")}>
            <span>👥</span> Staff
          </NavItem>
          <NavItem onClick={() => navigate("/announcements")}>
            <span>📢</span> Announcements
          </NavItem>
          <NavItem className="active">
            <span>📅</span> Leaves
          </NavItem>
          <NavItem>
            <span>📈</span> Reports
          </NavItem>
        </nav>
      </Sidebar>

      <Main>
        <TopBar>
          <PageTitle>Leave Management</PageTitle>
          <FilterTabs>
            <FilterTab 
              $active={activeFilter === 'all'} 
              onClick={() => setActiveFilter('all')}
            >
              All
            </FilterTab>
            <FilterTab 
              $active={activeFilter === 'pending'} 
              onClick={() => setActiveFilter('pending')}
            >
              Pending
            </FilterTab>
            <FilterTab 
              $active={activeFilter === 'approved'} 
              onClick={() => setActiveFilter('approved')}
            >
              Approved
            </FilterTab>
            <FilterTab 
              $active={activeFilter === 'rejected'} 
              onClick={() => setActiveFilter('rejected')}
            >
              Rejected
            </FilterTab>
          </FilterTabs>
        </TopBar>

        <LeaveTable>
          <TableHead>
            <tr>
              <th>Employee</th>
              <th>Leave Type</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Days</th>
              <th>Reason</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </TableHead>
          <TableBody>
            {filteredLeaves.map((leave) => (
              <tr key={leave.id}>
                <td>{leave.employeeName}</td>
                <td>{leave.leaveType}</td>
                <td>{leave.startDate}</td>
                <td>{leave.endDate}</td>
                <td>{leave.days}</td>
                <td>{leave.reason}</td>
                <td>
                  <StatusBadge $status={leave.status}>
                    {leave.status}
                  </StatusBadge>
                </td>
                <td>
                  {leave.status === 'pending' && (
                    <>
                      <ActionButton className="approve">Approve</ActionButton>
                      <ActionButton className="reject">Reject</ActionButton>
                    </>
                  )}
                  <ActionButton>View</ActionButton>
                </td>
              </tr>
            ))}
          </TableBody>
        </LeaveTable>
      </Main>
    </Layout>
  );
};

export default LeavesPage;
